#!/usr/bin/env python3
"""
Local test server for WhatsApp Group Exporter Chrome Extension
This creates a mock WhatsApp Web interface for testing the extension
"""

from http.server import HTTPServer, SimpleHTTPRequestHandler
import json
import os

class WhatsAppTestHandler(SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.path = '/test-whatsapp.html'
        elif self.path == '/whatsapp-mock':
            self.path = '/test-whatsapp.html'
        
        return SimpleHTTPRequestHandler.do_GET(self)

def run_server():
    port = 8080
    server_address = ('localhost', port)
    httpd = HTTPServer(server_address, WhatsAppTestHandler)
    
    print(f"Starting test server at http://localhost:{port}")
    print("Open this URL in Chrome to test the extension")
    print("Press Ctrl+C to stop the server")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped")
        httpd.server_close()

if __name__ == '__main__':
    run_server()
