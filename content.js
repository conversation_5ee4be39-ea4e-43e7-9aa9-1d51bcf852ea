// Content script for WhatsApp Web group extraction
console.log('WhatsApp Group Exporter content script loaded');

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'extractGroups') {
        console.log('Starting group extraction...');
        
        try {
            const groups = extractGroupNumbers();
            console.log('Extracted groups:', groups);
            sendResponse({ groups: groups });
        } catch (error) {
            console.error('Error extracting groups:', error);
            sendResponse({ error: error.message });
        }
    }
    return true; // Keep message channel open for async response
});

function extractGroupNumbers() {
    const groups = [];
    
    // Wait for WhatsApp to load
    if (!document.querySelector('[data-testid="chat-list"]')) {
        throw new Error('WhatsApp Web not fully loaded. Please wait and try again.');
    }

    // Method 1: Extract from chat list
    const chatItems = document.querySelectorAll('[data-testid="chat-list"] [data-testid="cell-frame-container"]');
    
    chatItems.forEach(chatItem => {
        try {
            // Check if it's a group (groups typically have group icons or multiple participant indicators)
            const titleElement = chatItem.querySelector('[data-testid="conversation-info-header"] span[title]');
            const groupIndicators = chatItem.querySelectorAll('[data-testid="group-icon"], [aria-label*="group"], [title*="group"]');
            
            if (titleElement && (groupIndicators.length > 0 || isLikelyGroup(titleElement.textContent))) {
                const groupName = titleElement.textContent.trim();
                
                // Try to extract phone number from various possible locations
                let phoneNumber = extractPhoneFromChat(chatItem);
                
                if (phoneNumber) {
                    groups.push({
                        name: groupName,
                        number: phoneNumber
                    });
                }
            }
        } catch (error) {
            console.warn('Error processing chat item:', error);
        }
    });

    // Method 2: If user is currently viewing a group, extract from current chat
    const currentChatHeader = document.querySelector('[data-testid="conversation-info-header"]');
    if (currentChatHeader) {
        try {
            const currentGroupInfo = extractCurrentGroupInfo();
            if (currentGroupInfo && !groups.find(g => g.name === currentGroupInfo.name)) {
                groups.push(currentGroupInfo);
            }
        } catch (error) {
            console.warn('Error extracting current group info:', error);
        }
    }

    return groups;
}

function extractPhoneFromChat(chatElement) {
    // Look for phone numbers in various formats
    const phoneRegex = /(\+?\d{1,4}[\s-]?\(?\d{1,4}\)?[\s-]?\d{1,4}[\s-]?\d{1,4}[\s-]?\d{1,9})/g;
    
    // Check in title attributes
    const titleElements = chatElement.querySelectorAll('[title]');
    for (let element of titleElements) {
        const title = element.getAttribute('title');
        const phoneMatch = title.match(phoneRegex);
        if (phoneMatch) {
            return cleanPhoneNumber(phoneMatch[0]);
        }
    }
    
    // Check in text content
    const textContent = chatElement.textContent;
    const phoneMatch = textContent.match(phoneRegex);
    if (phoneMatch) {
        return cleanPhoneNumber(phoneMatch[0]);
    }
    
    // Try to extract from data attributes or other sources
    const dataElements = chatElement.querySelectorAll('[data-id], [data-phone], [data-number]');
    for (let element of dataElements) {
        for (let attr of element.attributes) {
            if (attr.value.match(phoneRegex)) {
                return cleanPhoneNumber(attr.value.match(phoneRegex)[0]);
            }
        }
    }
    
    return null;
}

function extractCurrentGroupInfo() {
    const headerElement = document.querySelector('[data-testid="conversation-info-header"]');
    if (!headerElement) return null;
    
    const titleElement = headerElement.querySelector('span[title]');
    if (!titleElement) return null;
    
    const groupName = titleElement.textContent.trim();
    
    // Try to find group info by clicking on header (simulate)
    // This is more complex and might require user interaction
    // For now, we'll try to extract from available DOM elements
    
    const phoneNumber = extractPhoneFromElement(headerElement) || 
                       extractPhoneFromElement(document.querySelector('[data-testid="conversation-panel-wrapper"]'));
    
    if (phoneNumber && isLikelyGroup(groupName)) {
        return {
            name: groupName,
            number: phoneNumber
        };
    }
    
    return null;
}

function extractPhoneFromElement(element) {
    if (!element) return null;
    
    const phoneRegex = /(\+?\d{1,4}[\s-]?\(?\d{1,4}\)?[\s-]?\d{1,4}[\s-]?\d{1,4}[\s-]?\d{1,9})/g;
    
    // Check all text content
    const textContent = element.textContent;
    const phoneMatch = textContent.match(phoneRegex);
    if (phoneMatch) {
        return cleanPhoneNumber(phoneMatch[0]);
    }
    
    // Check attributes
    const allElements = element.querySelectorAll('*');
    for (let el of allElements) {
        for (let attr of el.attributes) {
            const phoneMatch = attr.value.match(phoneRegex);
            if (phoneMatch) {
                return cleanPhoneNumber(phoneMatch[0]);
            }
        }
    }
    
    return null;
}

function isLikelyGroup(name) {
    // Heuristics to determine if a chat is likely a group
    const groupKeywords = ['group', 'team', 'family', 'friends', 'class', 'work', 'project'];
    const lowerName = name.toLowerCase();
    
    // Check for group keywords
    if (groupKeywords.some(keyword => lowerName.includes(keyword))) {
        return true;
    }
    
    // Groups often have multiple words or special characters
    if (name.includes(' ') && name.length > 10) {
        return true;
    }
    
    // Check for common group naming patterns
    if (/\d{4}|\d{2,}/.test(name)) { // Contains year or multiple digits
        return true;
    }
    
    return false;
}

function cleanPhoneNumber(phone) {
    // Remove common formatting and keep only digits and +
    return phone.replace(/[\s\-\(\)]/g, '').replace(/^(\+)/, '+');
}

// Alternative method: Monitor for group info panels
function observeGroupInfoPanels() {
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                // Look for group info panels that might contain phone numbers
                const groupInfoPanels = document.querySelectorAll('[data-testid="group-info-panel"], [data-testid="contact-info-panel"]');
                groupInfoPanels.forEach(panel => {
                    // Extract information when panels are opened
                    console.log('Group info panel detected:', panel);
                });
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}
