// Test script to simulate the extension extraction on our test page
// This will help us debug the extraction logic

console.log('🧪 Testing WhatsApp Group Extraction...');

// Simulate the extraction function from content.js
function testExtractGroupNumbers() {
    const groups = [];
    
    // Check if chat list exists
    const chatList = document.querySelector('[data-testid="chat-list"]');
    if (!chatList) {
        console.error('❌ Chat list not found');
        return [];
    }
    console.log('✅ Chat list found');

    // Get all chat items
    const chatItems = document.querySelectorAll('[data-testid="chat-list"] [data-testid="cell-frame-container"]');
    console.log(`📋 Found ${chatItems.length} chat items`);
    
    chatItems.forEach((chatItem, index) => {
        console.log(`\n🔍 Processing chat item ${index + 1}:`);
        
        try {
            // Find title element
            const titleElement = chatItem.querySelector('[data-testid="conversation-info-header"] span[title]');
            if (!titleElement) {
                console.log('  ❌ No title element found');
                return;
            }
            
            const groupName = titleElement.textContent.trim();
            console.log(`  📝 Chat name: "${groupName}"`);
            
            // Check for group indicators
            const groupIndicators = chatItem.querySelectorAll('[data-testid="group-icon"], [aria-label*="group"], [title*="group"]');
            console.log(`  🔍 Group indicators found: ${groupIndicators.length}`);
            
            // Check if it's likely a group
            const isGroup = groupIndicators.length > 0 || isLikelyGroup(groupName);
            console.log(`  🤔 Is likely group: ${isGroup}`);
            
            if (isGroup) {
                // Try to extract phone number
                const phoneNumber = extractPhoneFromChat(chatItem);
                console.log(`  📞 Phone number: ${phoneNumber || 'Not found'}`);
                
                if (phoneNumber) {
                    groups.push({
                        name: groupName,
                        number: phoneNumber
                    });
                    console.log(`  ✅ Added group: ${groupName} - ${phoneNumber}`);
                } else {
                    console.log(`  ⚠️ Group found but no phone number: ${groupName}`);
                }
            } else {
                console.log(`  ℹ️ Skipped (not a group): ${groupName}`);
            }
        } catch (error) {
            console.error(`  ❌ Error processing chat item:`, error);
        }
    });

    return groups;
}

function extractPhoneFromChat(chatElement) {
    const phoneRegex = /(\+?\d{1,4}[\s-]?\(?\d{1,4}\)?[\s-]?\d{1,4}[\s-]?\d{1,4}[\s-]?\d{1,9})/g;
    
    console.log('    🔍 Looking for phone numbers...');
    
    // Check in text content first
    const textContent = chatElement.textContent;
    console.log(`    📄 Text content: "${textContent}"`);
    
    const phoneMatch = textContent.match(phoneRegex);
    if (phoneMatch) {
        console.log(`    📞 Found phone in text: ${phoneMatch[0]}`);
        return cleanPhoneNumber(phoneMatch[0]);
    }
    
    // Check in title attributes
    const titleElements = chatElement.querySelectorAll('[title]');
    for (let element of titleElements) {
        const title = element.getAttribute('title');
        const phoneMatch = title.match(phoneRegex);
        if (phoneMatch) {
            console.log(`    📞 Found phone in title: ${phoneMatch[0]}`);
            return cleanPhoneNumber(phoneMatch[0]);
        }
    }
    
    console.log('    ❌ No phone number found');
    return null;
}

function isLikelyGroup(name) {
    const groupKeywords = ['group', 'team', 'family', 'friends', 'class', 'work', 'project'];
    const lowerName = name.toLowerCase();
    
    // Check for group keywords
    if (groupKeywords.some(keyword => lowerName.includes(keyword))) {
        return true;
    }
    
    // Groups often have multiple words or special characters
    if (name.includes(' ') && name.length > 10) {
        return true;
    }
    
    // Check for common group naming patterns
    if (/\d{4}|\d{2,}/.test(name)) {
        return true;
    }
    
    // Arabic text is likely a group if it's long enough
    if (/[\u0600-\u06FF]/.test(name) && name.length > 5) {
        return true;
    }
    
    return false;
}

function cleanPhoneNumber(phone) {
    return phone.replace(/[\s\-\(\)]/g, '').replace(/^(\+)/, '+');
}

// Run the test when page is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(() => {
            console.log('\n🚀 Starting extraction test...');
            const results = testExtractGroupNumbers();
            console.log('\n📊 Final Results:');
            console.log(results);
            
            if (results.length > 0) {
                console.log('\n✅ SUCCESS! Groups extracted:');
                results.forEach((group, index) => {
                    console.log(`${index + 1}. ${group.name} - ${group.number}`);
                });
            } else {
                console.log('\n❌ No groups were extracted');
            }
        }, 1000);
    });
} else {
    setTimeout(() => {
        console.log('\n🚀 Starting extraction test...');
        const results = testExtractGroupNumbers();
        console.log('\n📊 Final Results:');
        console.log(results);
        
        if (results.length > 0) {
            console.log('\n✅ SUCCESS! Groups extracted:');
            results.forEach((group, index) => {
                console.log(`${index + 1}. ${group.name} - ${group.number}`);
            });
        } else {
            console.log('\n❌ No groups were extracted');
        }
    }, 1000);
}
