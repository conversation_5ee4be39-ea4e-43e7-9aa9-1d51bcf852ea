document.addEventListener('DOMContentLoaded', function() {
    const extractBtn = document.getElementById('extractBtn');
    const exportBtn = document.getElementById('exportBtn');
    const statusDiv = document.getElementById('status');
    const resultsDiv = document.getElementById('results');
    
    let extractedGroups = [];

    extractBtn.addEventListener('click', async function() {
        try {
            // Check if we're on WhatsApp Web
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (!tab.url.includes('web.whatsapp.com')) {
                showStatus('Please navigate to WhatsApp Web first!', 'error');
                return;
            }

            showStatus('Extracting group numbers...', 'info');
            extractBtn.disabled = true;

            // Inject and execute the extraction script
            const results = await chrome.tabs.sendMessage(tab.id, { action: 'extractGroups' });
            
            if (results && results.groups && results.groups.length > 0) {
                extractedGroups = results.groups;
                displayResults(extractedGroups);
                showStatus(`Found ${extractedGroups.length} groups!`, 'success');
                exportBtn.style.display = 'block';
            } else {
                showStatus('No groups found. Make sure WhatsApp Web is fully loaded.', 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            showStatus('Error extracting groups. Please try again.', 'error');
        } finally {
            extractBtn.disabled = false;
        }
    });

    exportBtn.addEventListener('click', function() {
        if (extractedGroups.length === 0) {
            showStatus('No groups to export!', 'error');
            return;
        }

        // Create CSV content
        const csvContent = 'Group Name,Phone Number\n' + 
            extractedGroups.map(group => `"${group.name}","${group.number}"`).join('\n');

        // Create and download file
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'whatsapp_groups.csv';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        showStatus('CSV file downloaded!', 'success');
    });

    function showStatus(message, type) {
        statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
    }

    function displayResults(groups) {
        resultsDiv.innerHTML = '<h4>Extracted Groups:</h4>' +
            groups.map(group => `
                <div class="group-item">
                    <div class="group-name">${group.name}</div>
                    <div class="group-number">${group.number}</div>
                </div>
            `).join('');
    }
});
