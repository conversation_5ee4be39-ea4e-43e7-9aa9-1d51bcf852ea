<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Web - Test Environment</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #111b21;
            color: white;
        }
        .container {
            display: flex;
            height: 100vh;
        }
        .sidebar {
            width: 30%;
            background-color: #202c33;
            border-right: 1px solid #3b4a54;
        }
        .chat-area {
            flex: 1;
            background-color: #0b141a;
        }
        .header {
            padding: 20px;
            background-color: #2a3942;
            text-align: center;
        }
        .chat-list {
            padding: 10px;
        }
        .chat-item {
            padding: 12px;
            margin: 5px 0;
            background-color: #2a3942;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .chat-item:hover {
            background-color: #3b4a54;
        }
        .chat-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .chat-preview {
            font-size: 14px;
            color: #8696a0;
        }
        .group-icon {
            display: inline-block;
            width: 12px;
            height: 12px;
            background-color: #25d366;
            border-radius: 50%;
            margin-right: 8px;
        }
        .conversation-header {
            padding: 15px 20px;
            background-color: #2a3942;
            border-bottom: 1px solid #3b4a54;
        }
        .conversation-info {
            display: flex;
            align-items: center;
        }
        .conversation-title {
            font-weight: bold;
            font-size: 16px;
        }
        .test-info {
            background-color: #1f2937;
            padding: 20px;
            margin: 20px;
            border-radius: 8px;
            border-left: 4px solid #25d366;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h3>🧪 WhatsApp Web Test Environment</h3>
        <p>This is a mock WhatsApp Web interface for testing the Chrome extension.</p>
        <p><strong>To test:</strong> Install the extension, then click the extension icon and try "Extract Group Numbers"</p>
    </div>

    <div class="container">
        <div class="sidebar">
            <div class="header">
                <h2>Chats</h2>
            </div>
            <div class="chat-list" data-testid="chat-list">
                <!-- Individual Chat -->
                <div class="chat-item" data-testid="cell-frame-container">
                    <div data-testid="conversation-info-header">
                        <div class="chat-name">
                            <span title="Ahmed Ali">Ahmed Ali</span>
                        </div>
                        <div class="chat-preview">Hello, how are you?</div>
                    </div>
                </div>

                <!-- Group Chat 1 - مجتمع زد الذهبي -->
                <div class="chat-item" data-testid="cell-frame-container">
                    <div data-testid="conversation-info-header">
                        <div class="chat-name">
                            <span class="group-icon" data-testid="group-icon"></span>
                            <span title="مجتمع زد الذهبي">مجتمع زد الذهبي</span>
                        </div>
                        <div class="chat-preview">Welcome to our community! +966501234567</div>
                    </div>
                </div>

                <!-- Group Chat 2 -->
                <div class="chat-item" data-testid="cell-frame-container">
                    <div data-testid="conversation-info-header">
                        <div class="chat-name">
                            <span class="group-icon" data-testid="group-icon"></span>
                            <span title="Family Group 2024">Family Group 2024</span>
                        </div>
                        <div class="chat-preview">Family reunion planning +1234567890</div>
                    </div>
                </div>

                <!-- Group Chat 3 -->
                <div class="chat-item" data-testid="cell-frame-container">
                    <div data-testid="conversation-info-header">
                        <div class="chat-name">
                            <span class="group-icon" data-testid="group-icon"></span>
                            <span title="Work Team Project Alpha">Work Team Project Alpha</span>
                        </div>
                        <div class="chat-preview">Meeting tomorrow at 10 AM +44207123456</div>
                    </div>
                </div>

                <!-- Individual Chat 2 -->
                <div class="chat-item" data-testid="cell-frame-container">
                    <div data-testid="conversation-info-header">
                        <div class="chat-name">
                            <span title="Sarah Johnson">Sarah Johnson</span>
                        </div>
                        <div class="chat-preview">Thanks for the help!</div>
                    </div>
                </div>

                <!-- Group Chat 4 -->
                <div class="chat-item" data-testid="cell-frame-container">
                    <div data-testid="conversation-info-header">
                        <div class="chat-name">
                            <span class="group-icon" data-testid="group-icon"></span>
                            <span title="University Class 2024">University Class 2024</span>
                        </div>
                        <div class="chat-preview">Assignment due next week +971501234567</div>
                    </div>
                </div>

                <!-- Group Chat 5 -->
                <div class="chat-item" data-testid="cell-frame-container">
                    <div data-testid="conversation-info-header">
                        <div class="chat-name">
                            <span class="group-icon" data-testid="group-icon"></span>
                            <span title="Gaming Friends">Gaming Friends</span>
                        </div>
                        <div class="chat-preview">Anyone up for a game tonight? +49301234567</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="chat-area">
            <div class="conversation-header" data-testid="conversation-info-header">
                <div class="conversation-info">
                    <span class="group-icon" data-testid="group-icon"></span>
                    <span class="conversation-title" title="مجتمع زد الذهبي">مجتمع زد الذهبي</span>
                </div>
            </div>
            <div style="padding: 20px; text-align: center; color: #8696a0;">
                <p>This is a test environment for the WhatsApp Group Exporter extension.</p>
                <p>The extension should be able to extract the following groups:</p>
                <ul style="text-align: left; max-width: 400px; margin: 0 auto;">
                    <li><strong>مجتمع زد الذهبي</strong> - +966501234567</li>
                    <li><strong>Family Group 2024</strong> - +1234567890</li>
                    <li><strong>Work Team Project Alpha</strong> - +44207123456</li>
                    <li><strong>University Class 2024</strong> - +971501234567</li>
                    <li><strong>Gaming Friends</strong> - +49301234567</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Add some interactivity to make it feel more like WhatsApp Web
        document.querySelectorAll('.chat-item').forEach(item => {
            item.addEventListener('click', function() {
                // Remove active class from all items
                document.querySelectorAll('.chat-item').forEach(i => i.classList.remove('active'));
                // Add active class to clicked item
                this.classList.add('active');
                
                // Update conversation header
                const chatName = this.querySelector('span[title]').getAttribute('title');
                document.querySelector('.conversation-title').textContent = chatName;
                document.querySelector('.conversation-title').setAttribute('title', chatName);
            });
        });

        // Log when the page is ready for extension testing
        console.log('WhatsApp Web test environment loaded');
        console.log('Extension should be able to detect groups now');
    </script>

    <!-- Test script for debugging extraction -->
    <script src="test-extraction.js"></script>
</body>
</html>
