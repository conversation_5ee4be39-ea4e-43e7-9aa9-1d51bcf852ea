# WhatsApp Group Number Exporter

A Chrome extension that extracts WhatsApp group numbers from WhatsApp Web.

## Features

- Extract group names and phone numbers from WhatsApp Web
- Export data to CSV format
- Simple and user-friendly interface
- Works directly with WhatsApp Web

## Installation

1. Clone or download this repository
2. Open Chrome and go to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the extension folder
5. The extension icon will appear in your Chrome toolbar

## Usage

1. Open WhatsApp Web (https://web.whatsapp.com) in Chrome
2. Make sure WhatsApp Web is fully loaded with your chats visible
3. Click the extension icon in the Chrome toolbar
4. Click "Extract Group Numbers" to scan for groups
5. Review the extracted groups in the popup
6. Click "Export to CSV" to download the data

## How it Works

The extension uses content scripts to analyze the WhatsApp Web interface and extract:
- Group names from chat titles
- Associated phone numbers from various DOM elements
- Filters results to focus on group chats (vs individual chats)

## Important Notes

- **Privacy**: This extension only works locally in your browser and doesn't send data anywhere
- **Limitations**: WhatsApp Web's structure may change, which could affect extraction accuracy
- **Groups Only**: The extension attempts to identify and extract only group chats
- **Phone Number Availability**: Not all groups may have easily extractable phone numbers depending on WhatsApp's current interface

## File Structure

```
├── manifest.json       # Extension configuration
├── popup.html         # Extension popup interface
├── popup.js          # Popup functionality
├── content.js        # WhatsApp Web content script
├── icon16.png        # Extension icon (16x16)
├── icon48.png        # Extension icon (48x48)
├── icon128.png       # Extension icon (128x128)
└── README.md         # This file
```

## Troubleshooting

1. **No groups found**: Make sure WhatsApp Web is fully loaded and you can see your chat list
2. **Extension not working**: Try refreshing the WhatsApp Web page and try again
3. **Missing phone numbers**: Some groups may not have easily accessible phone numbers in the current WhatsApp Web interface

## Development

To modify or enhance the extension:

1. Edit the relevant files
2. Go to `chrome://extensions/`
3. Click the refresh icon on the extension card
4. Test your changes

## Disclaimer

This extension is for educational and personal use only. Please respect WhatsApp's terms of service and user privacy when using this tool.
