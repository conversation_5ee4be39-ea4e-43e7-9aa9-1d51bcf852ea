# 🚀 Installation and Testing Guide

## Quick Start

### 1. Install the Chrome Extension

1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode" (toggle in top right corner)
3. Click "Load unpacked" button
4. Select this folder (`/Users/<USER>/Documents/augment-projects/zidnu`)
5. You should see "WhatsApp Group Number Exporter" extension loaded

### 2. Test with Local Environment

The test server is already running at: **http://localhost:8080**

1. **Open the test page** (should already be open in your browser)
2. **Open Developer Console** (F12 or Cmd+Option+I)
3. **Look for test results** in the console - you should see extraction test results
4. **Click the extension icon** in Chrome toolbar (green WhatsApp-style icon)
5. **Click "Extract Group Numbers"** in the popup
6. **Check results** - you should see 5 groups including "مجتمع زد الذهبي"

### 3. Expected Results

The extension should extract these groups:

✅ **مجتمع زد الذهبي** - +************  
✅ **Family Group 2024** - +1234567890  
✅ **Work Team Project Alpha** - +44207123456  
✅ **University Class 2024** - +971501234567  
✅ **Gaming Friends** - +49301234567  

❌ Individual chats should be filtered out (Ahmed Ali, Sarah Johnson)

### 4. Export to CSV

1. After extraction, click "Export to CSV"
2. A file named `whatsapp_groups.csv` will be downloaded
3. Open the CSV file to verify the data

## 🐛 Troubleshooting

### Extension Not Working?

1. **Check Console Errors**:
   - Open Developer Console (F12)
   - Look for any red error messages
   - Check both the main page console and extension popup console

2. **Reload Extension**:
   - Go to `chrome://extensions/`
   - Click the refresh icon on the extension card
   - Try again

3. **Check Permissions**:
   - Make sure the extension has permissions for `http://localhost:8080/*`
   - Check the manifest.json file

### No Groups Found?

1. **Check Test Page**:
   - Make sure you're on http://localhost:8080
   - Verify the test groups are visible on the page
   - Check browser console for test extraction results

2. **Check Extension Console**:
   - Right-click extension icon → "Inspect popup"
   - Look for error messages in the popup console

## 🔧 Development

### Files Structure:
- `manifest.json` - Extension configuration
- `popup.html` - Extension popup interface  
- `popup.js` - Popup functionality
- `content.js` - WhatsApp Web content script
- `test-whatsapp.html` - Mock WhatsApp Web interface
- `test-extraction.js` - Debug extraction logic
- `test-server.py` - Local test server

### Making Changes:
1. Edit the relevant files
2. Go to `chrome://extensions/`
3. Click refresh icon on extension
4. Test changes

## 📞 Test with Real WhatsApp Web

Once the extension works with the test environment:

1. Go to https://web.whatsapp.com
2. Sign in with your phone
3. Wait for chats to load
4. Use the extension to extract real group numbers

**Note**: The real WhatsApp Web interface may have different DOM structure, so some adjustments might be needed.
