<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 350px;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .button {
            background-color: #25D366;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            width: 100%;
            font-size: 16px;
            margin-bottom: 10px;
        }
        .button:hover {
            background-color: #128C7E;
        }
        .button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .results {
            margin-top: 15px;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
        }
        .group-item {
            margin-bottom: 10px;
            padding: 8px;
            background-color: #f8f9fa;
            border-radius: 3px;
        }
        .group-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .group-number {
            font-family: monospace;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h3>WhatsApp Group Exporter</h3>
        <p>Extract group numbers from WhatsApp Web</p>
    </div>
    
    <button id="extractBtn" class="button">Extract Group Numbers</button>
    <button id="exportBtn" class="button" style="display: none;">Export to CSV</button>
    
    <div id="status"></div>
    <div id="results"></div>
    
    <script src="popup.js"></script>
</body>
</html>
